import { HoldMessageModal } from "@/components/hold-message-modal";
import { ConfirmDialog } from "@/components/modal";
import { ProblemModal } from "@/components/problem-modal";
import { useParams } from "@tanstack/react-router";
import { useState } from "react";
import { getConversationProfile } from "../api";
import CenterChatPanel from "./center-chat-panel";
import LeftPanel from "./left-panel";
import RightPanel from "./right-panel";

export default function ChatProfileView() {
  const { conversationId } = useParams({
    from: "/_authenticated/sessions/chat-mod/$conversationId",
  });
  const [show, setShow] = useState(false);
  const [showHold, setShowHold] = useState(false);
  const [showProblem, setShowProblem] = useState(false);

  const {
    data: {
      customer = {},
      model = {},
      messages: { messages = {}, meta = {} } = {},
    } = {},
  } = getConversationProfile(conversationId);

  return (
    <div className="grid grid-cols-1 xl:grid-cols-[1fr_2fr_1fr] h-screen w-full gap-[16px] px-3 py-4 sm:px-4 sm:py-6">
      <LeftPanel data={model} />

      <CenterChatPanel
        setShowHold={setShowHold}
        setShowProblem={setShowProblem}
      />

      <RightPanel data={customer} setShow={setShow} />

      <ProblemModal
        open={showProblem}
        onOpenChange={setShowProblem}
        isLoading={false}
        onSave={() => {}}
      />

      <HoldMessageModal
        open={showHold}
        onOpenChange={setShowHold}
        isLoading={false}
        onSave={() => {}}
      />

      <ConfirmDialog open={show} setOpen={setShow} data={customer} />
    </div>
  );
}
