import UserImg from "@/assets/user.png";
import {
  IconArrowDown,
  IconArrowUp,
  IconClock,
  IconDashboard,
  IconGift,
  IconLanguage,
  IconList,
  IconMessageReply,
  IconMoodSmile,
  IconPaperclip,
  IconSearch,
  IconShareplay,
  IconUsersGroup,
} from "@tabler/icons-react";

interface CenterChatPanelProps {
  setShowHold: (value: boolean) => void;
  setShowProblem: (value: boolean) => void;
}

export default function CenterChatPanel({
  setShowHold,
  setShowProblem,
}: CenterChatPanelProps) {
  return (
    <div className="flex flex-col rounded-2xl">
      <div className="flex items-center gap-3 p-4 bg-[#171717] border-b rounded-t-2xl">
        <div className="flex items-center bg-[#444] px-3 py-2 rounded-md text-white w-full max-w-md">
          <IconSearch size={16} className="mr-2 text-gray-300" />
          <input
            type="text"
            placeholder="Search Keyword"
            className="bg-transparent text-sm text-white placeholder-gray-300 outline-none w-full"
          />
        </div>

        <div className="flex items-center bg-[#252525] text-white text-sm px-4 py-2 rounded-xs gap-3">
          <span className="flex items-center gap-1">
            00 <IconList size={14} />
          </span>
          <div className="border-l border-gray-400 h-4" />
          <span className="flex items-center gap-1">
            08 <IconArrowDown size={14} />
          </span>
          <div className="border-l border-gray-400 h-4" />
          <span className="flex items-center gap-1">
            09 <IconArrowUp size={14} />
          </span>
        </div>

        <button
          onClick={() => setShowHold(true)}
          className="bg-white text-gray-700 text-sm px-4 py-2 rounded-xs cursor-pointer"
        >
          Hold
        </button>
      </div>

      <div className="flex-1 bg-sidebar p-4 space-y-4 overflow-y-auto max-h-[calc(100dvh-350px)]">
        <div className="Received-msg flex gap-2 w-full max-w-[85%]">
          <img
            src={UserImg}
            alt="user"
            className="w-[40px] h-[40px] rounded-full"
          />
          <div className="flex flex-col gap-2">
            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">
              next time you'll be awake at this hour why not now
            </div>
            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">
              Didn't I tell you not to put your phone on charge just because
              it's the weekend?
            </div>
            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">
              🥰🥰😘😘❤️❤️
            </div>
            <div className="text-xs text-gray-500">Sat 5:10 AM</div>
          </div>
        </div>
        <div className="sent-msg flex justify-end gap-2 w-full ms-auto max-w-[85%]">
          <div className="flex flex-col items-end gap-2">
            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-se-none bg-sidebar-primary text-sm text-white">
              next time you'll be awake at this hour why not now next time
              you'll be awake at this hour why not nownext time you'll be awake
              at this hour why not now
            </div>
            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-se-none bg-sidebar-primary text-sm text-white">
              🥰🥰😘😘❤️❤️
            </div>
            <div className="text-xs text-gray-500 text-end">Sat 5:15 AM</div>
          </div>
          <img
            src={UserImg}
            alt="user"
            className="w-[40px] h-[40px] rounded-full"
          />
        </div>

        <div className="text-xs text-gray-600 bg-sidebar text-center mt-4 w-fit px-4 py-2 rounded-full mx-auto">
          Tuesday Dec 2, 2024
        </div>

        <div className="Received-msg flex gap-2 w-full max-w-[85%]">
          <img
            src={UserImg}
            alt="user"
            className="w-[40px] h-[40px] rounded-full"
          />
          <div className="flex flex-col gap-2">
            <div className="w-fit break-words px-4 py-3 rounded-xl rounded-ss-none bg-sidebar-accent text-sm">
              next time you'll be awake at this hour why not now
            </div>
            <div className="text-xs text-gray-500">Sat 5:10 AM</div>
          </div>
        </div>
      </div>

      <div className="w-full p-4 space-y-4 bg-sidebar rounded-b-2xl">
        <div className="flex gap-2 mb-2">
          <button className="flex items-center gap-1 px-4 py-2 text-sm bg-sidebar-accent rounded-md">
            <IconClock size={16} />
            266 Sec
          </button>
          <button className="px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
            + 60 Sec
          </button>
        </div>

        <div className="flex items-center justify-between border rounded-xl px-4 py-3 bg-sidebar mb-2">
          <input
            type="text"
            placeholder="Enter Message"
            className="flex-1 outline-none text-md bg-transparent placeholder-gray-400"
          />
          <div className="flex items-center gap-3 text-gray-500">
            <IconLanguage size={24} />
            <IconPaperclip size={24} />
            <IconGift size={24} />
            <IconMoodSmile size={24} />
          </div>
        </div>

        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="flex gap-2">
            <button className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
              <IconShareplay size={16} />
              Reply & Stay
            </button>
            <button className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
              <IconMessageReply size={16} />
              Reply
            </button>
            <button className="flex items-center gap-1 px-4 py-2 text-sm text-white bg-sidebar-primary rounded-md">
              <IconDashboard size={16} />
              Lobby
            </button>
          </div>
          <button
            onClick={() => setShowProblem(true)}
            className="flex items-center gap-1 px-4 py-2 text-sm border rounded-md cursor-pointer"
          >
            <IconUsersGroup size={16} />
            Problem
          </button>
        </div>
      </div>
    </div>
  );
}
