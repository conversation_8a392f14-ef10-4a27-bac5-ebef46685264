import { Main } from "@/components/layout/main";
import { columns } from "./components/loby-columns";
import { LobyTable } from "./components/loby-table";
import ModelsProvider from "./context/models-context"; 

export default function List() {
  
  return (
    <ModelsProvider>
      <Main>
        <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Available Sessions
            </h2>
          </div>
        </div>
        <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
          <LobyTable columns={columns} />
        </div>
      </Main>
    </ModelsProvider>
  );
}
